import {
  signInWithCredential,
  GoogleAuthProvider,
  OAuthProvider,
  PhoneAuthProvider,
  linkWithCredential,
  signInAnonymously,
  signInWithCustomToken,
} from 'firebase/auth';
import * as AuthSession from 'expo-auth-session';
import * as AppleAuthentication from 'expo-apple-authentication';
import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';
import { authService } from '../auth';
import { mongoService } from '../mongodb';

// Mock Firebase Auth
jest.mock('firebase/auth');
jest.mock('../mongodb');
jest.mock('../config/firebase', () => ({
  auth: {
    currentUser: null,
  },
}));

// Mock Expo modules
jest.mock('expo-auth-session', () => ({
  AuthRequest: jest.fn(),
  makeRedirectUri: jest.fn(),
  ResponseType: {
    IdToken: 'id_token',
  },
}));

jest.mock('expo-apple-authentication', () => ({
  isAvailableAsync: jest.fn(),
  signInAsync: jest.fn(),
  AppleAuthenticationScope: {
    FULL_NAME: 'fullName',
    EMAIL: 'email',
  },
}));

jest.mock('expo-local-authentication', () => ({
  hasHardwareAsync: jest.fn(),
  isEnrolledAsync: jest.fn(),
  authenticateAsync: jest.fn(),
}));

jest.mock('expo-secure-store', () => ({
  setItemAsync: jest.fn(),
  getItemAsync: jest.fn(),
}));

jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios',
  },
}));

// Mock Firebase Auth functions
const mockSignInWithCredential = jest.mocked(signInWithCredential);
const mockLinkWithCredential = jest.mocked(linkWithCredential);
const mockSignInAnonymously = jest.mocked(signInAnonymously);
const mockSignInWithCustomToken = jest.mocked(signInWithCustomToken);

// Mock Firebase Auth providers
const mockGoogleAuthProvider = {
  credential: jest.fn(),
} as any;
(GoogleAuthProvider as any).credential = mockGoogleAuthProvider.credential;

const mockOAuthProvider = jest.fn().mockImplementation(() => ({
  credential: jest.fn(),
}));
(OAuthProvider as any) = mockOAuthProvider;

const mockPhoneAuthProvider = {
  credential: jest.fn(),
} as any;
(PhoneAuthProvider as any).credential = mockPhoneAuthProvider.credential;

const mockAuthSession = AuthSession as jest.Mocked<typeof AuthSession>;
const mockAppleAuthentication = AppleAuthentication as jest.Mocked<typeof AppleAuthentication>;
const mockLocalAuthentication = LocalAuthentication as jest.Mocked<typeof LocalAuthentication>;
const mockSecureStore = SecureStore as jest.Mocked<typeof SecureStore>;
const mockMongoService = mongoService as jest.Mocked<typeof mongoService>;

describe('AuthService Enhanced Features', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Google Sign-In', () => {
    it('should sign in with Google successfully on native platforms', async () => {
      const mockUser = {
        uid: 'test-uid',
        email: '<EMAIL>',
        displayName: 'Test User',
      };

      const mockCredential = { providerId: 'google.com' };
      const mockUserCredential = { user: mockUser };

      // Mock AuthSession
      const mockRequest = {
        promptAsync: jest.fn().mockResolvedValue({
          type: 'success',
          params: { id_token: 'mock-id-token' },
        }),
      };
      mockAuthSession.AuthRequest.mockImplementation(() => mockRequest as any);
      mockAuthSession.makeRedirectUri.mockReturnValue('mock-redirect-uri');

      // Mock Google credential creation
      mockGoogleAuthProvider.credential.mockReturnValue(mockCredential as any);
      mockSignInWithCredential.mockResolvedValue(mockUserCredential as any);
      mockMongoService.getUserByAuthId.mockResolvedValue(null);
      mockMongoService.createUser.mockResolvedValue({} as any);

      // Set environment variable
      process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID_IOS = 'test-client-id';

      const result = await authService.signInWithGoogle();

      expect(result).toEqual(mockUser);
      expect(mockAuthSession.AuthRequest).toHaveBeenCalledWith({
        clientId: 'test-client-id',
        scopes: ['openid', 'profile', 'email'],
        redirectUri: 'mock-redirect-uri',
        responseType: AuthSession.ResponseType.IdToken,
      });
      expect(mockGoogleAuthProvider.credential).toHaveBeenCalledWith('mock-id-token');
      expect(mockSignInWithCredential).toHaveBeenCalledWith(expect.anything(), mockCredential);
    });

    it('should handle Google sign-in cancellation', async () => {
      const mockRequest = {
        promptAsync: jest.fn().mockResolvedValue({
          type: 'cancel',
        }),
      };
      mockAuthSession.AuthRequest.mockImplementation(() => mockRequest as any);
      process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID_IOS = 'test-client-id';

      await expect(authService.signInWithGoogle()).rejects.toThrow(
        'Google sign-in was cancelled or failed',
      );
    });

    it('should handle missing Google client ID', async () => {
      delete process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID_IOS;

      await expect(authService.signInWithGoogle()).rejects.toThrow(
        'Google client ID not configured',
      );
    });
  });

  describe('Apple Sign-In', () => {
    it('should sign in with Apple successfully on iOS', async () => {
      const mockUser = {
        uid: 'test-uid',
        email: '<EMAIL>',
        displayName: 'Test User',
      };

      const mockCredential = { providerId: 'apple.com' };
      const mockUserCredential = { user: mockUser };
      const mockAppleCredential = {
        identityToken: 'mock-identity-token',
        fullName: {
          givenName: 'Test',
          familyName: 'User',
        },
        realUserStatus: 1,
      };

      mockAppleAuthentication.isAvailableAsync.mockResolvedValue(true);
      mockAppleAuthentication.signInAsync.mockResolvedValue(mockAppleCredential as any);

      const mockProvider = {
        credential: jest.fn().mockReturnValue(mockCredential),
      };
      mockOAuthProvider.mockReturnValue(mockProvider as any);

      mockSignInWithCredential.mockResolvedValue(mockUserCredential as any);
      mockMongoService.getUserByAuthId.mockResolvedValue(null);
      mockMongoService.createUser.mockResolvedValue({} as any);

      const result = await authService.signInWithApple();

      expect(result).toEqual(mockUser);
      expect(mockAppleAuthentication.isAvailableAsync).toHaveBeenCalled();
      expect(mockAppleAuthentication.signInAsync).toHaveBeenCalledWith({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });
    });

    it('should handle Apple Sign-In not available', async () => {
      mockAppleAuthentication.isAvailableAsync.mockResolvedValue(false);

      await expect(authService.signInWithApple()).rejects.toThrow(
        'Apple Sign-In is not available on this device',
      );
    });

    it('should handle non-iOS platforms', async () => {
      (Platform as any).OS = 'android';

      await expect(authService.signInWithApple()).rejects.toThrow(
        'Apple Sign-In is only available on iOS 13+',
      );
    });
  });

  describe('Enhanced Phone Authentication', () => {
    it('should initiate phone authentication with anonymous user linking', async () => {
      const mockAnonymousUser = { uid: 'anonymous-uid', isAnonymous: true };
      const mockAnonymousCredential = { user: mockAnonymousUser };

      mockSignInAnonymously.mockResolvedValue(mockAnonymousCredential as any);

      // Mock web platform for this test
      (Platform as any).OS = 'web';

      await expect(authService.signInWithPhone('+**********')).rejects.toThrow(
        'Phone authentication on native platforms requires backend implementation',
      );

      expect(mockSignInAnonymously).toHaveBeenCalled();
    });

    it('should verify phone OTP and link to anonymous user', async () => {
      const mockAnonymousUser = { uid: 'anonymous-uid', isAnonymous: true };
      const mockUser = { uid: 'test-uid', phoneNumber: '+**********' };
      const mockCredential = { providerId: 'phone' };
      const mockUserCredential = { user: mockUser };

      // Mock current user as anonymous
      jest.spyOn(authService, 'getCurrentUser').mockReturnValue(mockAnonymousUser as any);

      mockPhoneAuthProvider.credential.mockReturnValue(mockCredential as any);
      mockLinkWithCredential.mockResolvedValue(mockUserCredential as any);
      mockMongoService.getUserByAuthId.mockResolvedValue(null);
      mockMongoService.createUser.mockResolvedValue({} as any);

      const profile = {
        firstName: 'Test',
        lastName: 'User',
        phoneNumber: '+**********',
        email: '',
        dateOfBirth: undefined,
        gender: undefined,
        location: undefined,
        bio: undefined,
        preferences: {
          notifications: { email: true, push: true, sms: false },
          privacy: { profileVisibility: 'public' as const, showEmail: false, showPhone: false },
          reading: { preferredTranslation: 'NIV' as const, fontSize: 'medium' as const, theme: 'auto' as const },
        },
      };

      const result = await authService.verifyPhoneOTP('verification-id', '123456', profile);

      expect(result).toEqual(mockUser);
      expect(mockPhoneAuthProvider.credential).toHaveBeenCalledWith('verification-id', '123456');
      expect(mockLinkWithCredential).toHaveBeenCalledWith(mockAnonymousUser, mockCredential);
    });
  });

  describe('Biometric Authentication', () => {
    it('should enable biometrics successfully', async () => {
      const mockUser = { uid: 'test-uid', getIdToken: jest.fn().mockResolvedValue('mock-token') };

      jest.spyOn(authService, 'getCurrentUser').mockReturnValue(mockUser as any);
      mockLocalAuthentication.hasHardwareAsync.mockResolvedValue(true);
      mockLocalAuthentication.isEnrolledAsync.mockResolvedValue(true);
      mockSecureStore.setItemAsync.mockResolvedValue();

      const result = await authService.enableBiometrics();

      expect(result).toBe(true);
      expect(mockLocalAuthentication.hasHardwareAsync).toHaveBeenCalled();
      expect(mockLocalAuthentication.isEnrolledAsync).toHaveBeenCalled();
      expect(mockUser.getIdToken).toHaveBeenCalled();
      expect(mockSecureStore.setItemAsync).toHaveBeenCalledWith('biometric_token', 'mock-token');
    });

    it('should handle biometric hardware not available', async () => {
      mockLocalAuthentication.hasHardwareAsync.mockResolvedValue(false);

      await expect(authService.enableBiometrics()).rejects.toThrow(
        'Biometric hardware not available',
      );
    });

    it('should handle no biometric credentials enrolled', async () => {
      mockLocalAuthentication.hasHardwareAsync.mockResolvedValue(true);
      mockLocalAuthentication.isEnrolledAsync.mockResolvedValue(false);

      await expect(authService.enableBiometrics()).rejects.toThrow(
        'No biometric credentials enrolled',
      );
    });

    it('should sign in with biometrics successfully', async () => {
      const mockUser = { uid: 'test-uid' };
      const mockUserCredential = { user: mockUser };

      mockLocalAuthentication.hasHardwareAsync.mockResolvedValue(true);
      mockLocalAuthentication.authenticateAsync.mockResolvedValue({ success: true } as any);
      mockSecureStore.getItemAsync.mockResolvedValue('stored-token');
      mockSignInWithCustomToken.mockResolvedValue(mockUserCredential as any);
      mockMongoService.updateUserLastLogin.mockResolvedValue();

      const result = await authService.signInWithBiometrics();

      expect(result).toEqual(mockUser);
      expect(mockLocalAuthentication.authenticateAsync).toHaveBeenCalledWith({
        promptMessage: 'Authenticate to sign in',
        fallbackLabel: 'Use passcode',
        cancelLabel: 'Cancel',
      });
      expect(mockSecureStore.getItemAsync).toHaveBeenCalledWith('biometric_token');
      expect(mockSignInWithCustomToken).toHaveBeenCalledWith(expect.anything(), 'stored-token');
    });

    it('should handle biometric authentication failure', async () => {
      mockLocalAuthentication.hasHardwareAsync.mockResolvedValue(true);
      mockLocalAuthentication.authenticateAsync.mockResolvedValue({ success: false } as any);

      await expect(authService.signInWithBiometrics()).rejects.toThrow(
        'Biometric authentication failed',
      );
    });

    it('should handle missing biometric credentials', async () => {
      mockLocalAuthentication.hasHardwareAsync.mockResolvedValue(true);
      mockLocalAuthentication.authenticateAsync.mockResolvedValue({ success: true } as any);
      mockSecureStore.getItemAsync.mockResolvedValue(null);

      await expect(authService.signInWithBiometrics()).rejects.toThrow(
        'No biometric credentials found. Please sign in normally first.',
      );
    });
  });
});
