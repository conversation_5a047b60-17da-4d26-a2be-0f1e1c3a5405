/**
 * Basic Auth Service Tests
 * Tests core authentication functionality without complex mocking
 */

describe('AuthService Basic Tests', () => {
  describe('Authentication Methods', () => {
    it('should have all required authentication methods', () => {
      // Import the auth service
      const { authService } = require('../auth');

      // Check that all required methods exist
      expect(typeof authService.signInWithEmail).toBe('function');
      expect(typeof authService.signUpWithEmail).toBe('function');
      expect(typeof authService.signInWithPhone).toBe('function');
      expect(typeof authService.verifyPhoneOTP).toBe('function');
      expect(typeof authService.signInWithGoogle).toBe('function');
      expect(typeof authService.signInWithApple).toBe('function');
      expect(typeof authService.signInWithBiometrics).toBe('function');
      expect(typeof authService.enableBiometrics).toBe('function');
      expect(typeof authService.signOut).toBe('function');
    });

    it('should have utility methods', () => {
      const { authService } = require('../auth');

      expect(typeof authService.getCurrentUser).toBe('function');
      expect(typeof authService.isAuthenticated).toBe('function');
      expect(typeof authService.refreshToken).toBe('function');
    });
  });

  describe('Error Handling', () => {
    it('should handle authentication errors properly', () => {
      const { authService } = require('../auth');

      // Test the private handleAuthError method through reflection
      const handleAuthError = authService.handleAuthError.bind(authService);
      
      const testError = new Error('Test error');
      testError.code = 'auth/test-error';
      
      const result = handleAuthError(testError);
      
      expect(result).toEqual({
        code: 'auth/test-error',
        message: 'Test error',
        details: undefined,
      });
    });

    it('should handle unknown errors', () => {
      const { authService } = require('../auth');

      const handleAuthError = authService.handleAuthError.bind(authService);
      const result = handleAuthError({});
      
      expect(result).toEqual({
        code: 'auth/unknown-error',
        message: 'An unknown error occurred',
        details: undefined,
      });
    });
  });

  describe('Platform Compatibility', () => {
    it('should handle Apple Sign-In platform restrictions', async () => {
      // Mock Platform.OS to be Android
      jest.doMock('react-native', () => ({
        Platform: { OS: 'android' },
      }));

      const { authService } = require('../auth');

      try {
        await authService.signInWithApple();
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).toContain('Apple Sign-In is only available on iOS 13+');
      }
    });
  });

  describe('Configuration Validation', () => {
    it('should validate Google client ID configuration', async () => {
      // Clear environment variables
      delete process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID_IOS;
      delete process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID_ANDROID;

      jest.doMock('react-native', () => ({
        Platform: { OS: 'ios' },
      }));

      const { authService } = require('../auth');

      try {
        await authService.signInWithGoogle();
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).toContain('Google client ID not configured');
      }
    });
  });

  describe('Method Signatures', () => {
    it('should have correct method signatures for phone auth', () => {
      const { authService } = require('../auth');

      // signInWithPhone should return a Promise<string> (verification ID)
      expect(authService.signInWithPhone).toBeDefined();
      
      // verifyPhoneOTP should accept verificationId, code, and optional profile
      expect(authService.verifyPhoneOTP).toBeDefined();
    });

    it('should have correct method signatures for social auth', () => {
      const { authService } = require('../auth');

      // Social auth methods should return Promise<AuthUser>
      expect(authService.signInWithGoogle).toBeDefined();
      expect(authService.signInWithApple).toBeDefined();
    });

    it('should have correct method signatures for biometric auth', () => {
      const { authService } = require('../auth');

      // enableBiometrics should return Promise<boolean>
      expect(authService.enableBiometrics).toBeDefined();
      
      // signInWithBiometrics should return Promise<AuthUser>
      expect(authService.signInWithBiometrics).toBeDefined();
    });
  });

  describe('Service Instantiation', () => {
    it('should export a singleton instance', () => {
      const { authService } = require('../auth');
      const { authService: authService2 } = require('../auth');

      expect(authService).toBe(authService2);
    });

    it('should export the AuthService class for testing', () => {
      const { AuthService } = require('../auth');

      expect(typeof AuthService).toBe('function');
      expect(AuthService.prototype.signInWithEmail).toBeDefined();
    });
  });
});

// Mock modules to prevent import errors
jest.mock('firebase/auth', () => ({
  signInWithEmailAndPassword: jest.fn(),
  createUserWithEmailAndPassword: jest.fn(),
  signOut: jest.fn(),
  sendPasswordResetEmail: jest.fn(),
  updateProfile: jest.fn(),
  deleteUser: jest.fn(),
  PhoneAuthProvider: { credential: jest.fn() },
  signInWithCredential: jest.fn(),
  RecaptchaVerifier: jest.fn(),
  signInWithPhoneNumber: jest.fn(),
  GoogleAuthProvider: { credential: jest.fn() },
  OAuthProvider: jest.fn(),
  linkWithCredential: jest.fn(),
  signInAnonymously: jest.fn(),
  signInWithCustomToken: jest.fn(),
}));

jest.mock('react-native', () => ({
  Platform: { OS: 'ios' },
}));

jest.mock('expo-auth-session', () => ({
  AuthRequest: jest.fn(),
  makeRedirectUri: jest.fn(),
  ResponseType: { IdToken: 'id_token' },
}));

jest.mock('expo-local-authentication', () => ({
  hasHardwareAsync: jest.fn(),
  isEnrolledAsync: jest.fn(),
  authenticateAsync: jest.fn(),
}));

jest.mock('expo-secure-store', () => ({
  setItemAsync: jest.fn(),
  getItemAsync: jest.fn(),
}));

jest.mock('../../config/firebase', () => ({
  auth: { currentUser: null },
}));

jest.mock('../mongodb', () => ({
  mongoService: {
    getUserByAuthId: jest.fn(),
    createUser: jest.fn(),
    updateUser: jest.fn(),
    updateUserLastLogin: jest.fn(),
    deleteUser: jest.fn(),
  },
}));
