import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  sendPasswordResetEmail,
  updateProfile,
  deleteUser,
  PhoneAuthProvider,
  signInWithCredential,
  RecaptchaVerifier,
  signInWithPhoneNumber,
  ConfirmationResult,
  User,
} from 'firebase/auth';
import { auth } from '../config/firebase';
import { mongoService } from './mongodb';
import { AuthUser, UserProfile, MongoUser, AuthError } from '../types/auth';
import { UserDocument } from '../types/database';

class AuthService {
  private recaptchaVerifier: RecaptchaVerifier | null = null;

  // Email Authentication
  async signInWithEmail(email: string, password: string): Promise<AuthUser> {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user as AuthUser;

      // Update last login in MongoDB
      await mongoService.updateUserLastLogin(user.uid);

      return user;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async signUpWithEmail(
    email: string,
    password: string,
    profile: UserProfile,
  ): Promise<AuthUser> {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user as AuthUser;

      // Update Firebase profile
      await updateProfile(user, {
        displayName: `${profile.firstName} ${profile.lastName}`,
      });

      // Create user document in MongoDB
      await this.createMongoUser(user.uid, {
        ...profile,
        email: user.email || email,
      });

      return user;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  // Phone Authentication
  async signInWithPhone(phoneNumber: string): Promise<ConfirmationResult> {
    try {
      // Note: For React Native, you might need to use a different approach
      // This is primarily for web. For React Native, consider using
      // react-native-firebase or Expo's phone auth

      if (!this.recaptchaVerifier) {
        this.recaptchaVerifier = new RecaptchaVerifier(
          'recaptcha-container',
          {
            size: 'invisible',
            callback: () => {
              // reCAPTCHA solved
            },
          },
          auth,
        );
      }

      const confirmationResult = await signInWithPhoneNumber(
        auth,
        phoneNumber,
        this.recaptchaVerifier,
      );

      return confirmationResult;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async verifyPhoneOTP(
    confirmationResult: ConfirmationResult,
    verificationCode: string,
    profile?: UserProfile,
  ): Promise<AuthUser> {
    try {
      const userCredential = await confirmationResult.confirm(verificationCode);
      const user = userCredential.user as AuthUser;

      // Check if user exists in MongoDB
      const existingUser = await mongoService.getUserByAuthId(user.uid);

      if (!existingUser && profile) {
        // Create new user document
        await this.createMongoUser(user.uid, {
          ...profile,
          phoneNumber: user.phoneNumber || undefined,
        });
      } else if (existingUser) {
        // Update last login
        await mongoService.updateUserLastLogin(user.uid);
      }

      return user;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  // Alternative phone auth using credential (for React Native)
  async verifyPhoneWithCredential(
    verificationId: string,
    verificationCode: string,
    profile?: UserProfile,
  ): Promise<AuthUser> {
    try {
      const credential = PhoneAuthProvider.credential(verificationId, verificationCode);
      const userCredential = await signInWithCredential(auth, credential);
      const user = userCredential.user as AuthUser;

      // Check if user exists in MongoDB
      const existingUser = await mongoService.getUserByAuthId(user.uid);

      if (!existingUser && profile) {
        // Create new user document
        await this.createMongoUser(user.uid, {
          ...profile,
          phoneNumber: user.phoneNumber || undefined,
        });
      } else if (existingUser) {
        // Update last login
        await mongoService.updateUserLastLogin(user.uid);
      }

      return user;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  // User Management
  async signOut(): Promise<void> {
    try {
      await firebaseSignOut(auth);

      // Clear recaptcha verifier
      if (this.recaptchaVerifier) {
        this.recaptchaVerifier.clear();
        this.recaptchaVerifier = null;
      }
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async sendPasswordReset(email: string): Promise<void> {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async updateUserProfile(profile: Partial<UserProfile>): Promise<void> {
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('No authenticated user');
      }

      // Update Firebase profile if name changed
      if (profile.firstName || profile.lastName) {
        await updateProfile(user, {
          displayName: `${profile.firstName || ''} ${profile.lastName || ''}`.trim(),
        });
      }

      // Update MongoDB user document
      await mongoService.updateUser(user.uid, { profile });
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async deleteAccount(): Promise<void> {
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('No authenticated user');
      }

      // Delete from MongoDB first
      const mongoUser = await mongoService.getUserByAuthId(user.uid);
      if (mongoUser && mongoUser._id) {
        await mongoService.deleteUser(mongoUser._id);
      }

      // Delete Firebase user
      await deleteUser(user);
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  // MongoDB Integration
  async getMongoUser(authId: string): Promise<MongoUser | null> {
    try {
      const user = await mongoService.getUserByAuthId(authId);
      return user as MongoUser | null;
    } catch (error) {
      console.error('Failed to get MongoDB user:', error);
      return null;
    }
  }

  private async createMongoUser(authId: string, profile: UserProfile): Promise<UserDocument> {
    const userData: Omit<UserDocument, '_id' | 'createdAt' | 'updatedAt'> = {
      authId,
      profile: {
        ...profile,
        preferences: profile.preferences || {
          notifications: {
            email: true,
            push: true,
            sms: false,
          },
          privacy: {
            profileVisibility: 'public',
            showEmail: false,
            showPhone: false,
          },
          reading: {
            preferredTranslation: 'NIV',
            fontSize: 'medium',
            theme: 'auto',
          },
        },
      },
      locale: 'en',
      lastLogin: new Date(),
      circleIds: [],
      isActive: true,
      emailVerified: false,
      phoneVerified: false,
    };

    return await mongoService.createUser(userData);
  }

  private handleAuthError(error: any): AuthError {
    const authError: AuthError = {
      code: error.code || 'auth/unknown-error',
      message: error.message || 'An unknown error occurred',
      details: error.details,
    };

    console.error('Auth Error:', authError);
    return authError;
  }

  // Utility methods
  getCurrentUser(): User | null {
    return auth.currentUser;
  }

  isAuthenticated(): boolean {
    return !!auth.currentUser;
  }

  async refreshToken(): Promise<string | null> {
    try {
      const user = auth.currentUser;
      if (user) {
        return await user.getIdToken(true);
      }
      return null;
    } catch (error) {
      console.error('Failed to refresh token:', error);
      return null;
    }
  }
}

// Singleton instance
export const authService = new AuthService();

// Export for testing
export { AuthService };
