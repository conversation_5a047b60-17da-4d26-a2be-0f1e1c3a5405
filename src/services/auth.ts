import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  sendPasswordResetEmail,
  updateProfile,
  deleteUser,
  PhoneAuthProvider,
  signInWithCredential,
  RecaptchaVerifier,
  signInWithPhoneNumber,
  ConfirmationResult,
  User,
  GoogleAuthProvider,
  OAuthProvider,
  linkWithCredential,
  signInAnonymously,
  signInWithCustomToken,
} from 'firebase/auth';
import { Platform } from 'react-native';
import * as AuthSession from 'expo-auth-session';
import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';

// Platform-specific imports with guards
let AppleAuthentication: typeof import('expo-apple-authentication') | null = null;
if (Platform.OS === 'ios') {
  try {
    AppleAuthentication = require('expo-apple-authentication');
  } catch (error) {
    console.warn('Apple Authentication not available:', error);
  }
}
import { auth } from '../config/firebase';
import { mongoService } from './mongodb';
import { AuthUser, UserProfile, MongoUser, AuthError } from '../types/auth';
import { UserDocument } from '../types/database';

class AuthService {
  private recaptchaVerifier: RecaptchaVerifier | null = null;

  // Email Authentication
  async signInWithEmail(email: string, password: string): Promise<AuthUser> {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user as AuthUser;

      // Update last login in MongoDB
      await mongoService.updateUserLastLogin(user.uid);

      return user;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async signUpWithEmail(
    email: string,
    password: string,
    profile: UserProfile,
  ): Promise<AuthUser> {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user as AuthUser;

      // Update Firebase profile
      await updateProfile(user, {
        displayName: `${profile.firstName} ${profile.lastName}`,
      });

      // Create user document in MongoDB
      await this.createMongoUser(user.uid, {
        ...profile,
        email: user.email || email,
      });

      return user;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  // Phone Authentication
  async signInWithPhone(phoneNumber: string): Promise<ConfirmationResult> {
    try {
      // Note: For React Native, you might need to use a different approach
      // This is primarily for web. For React Native, consider using
      // react-native-firebase or Expo's phone auth

      if (!this.recaptchaVerifier) {
        this.recaptchaVerifier = new RecaptchaVerifier(
          'recaptcha-container',
          {
            size: 'invisible',
            callback: () => {
              // reCAPTCHA solved
            },
          },
          auth,
        );
      }

      const confirmationResult = await signInWithPhoneNumber(
        auth,
        phoneNumber,
        this.recaptchaVerifier,
      );

      return confirmationResult;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async verifyPhoneOTP(
    confirmationResult: ConfirmationResult,
    verificationCode: string,
    profile?: UserProfile,
  ): Promise<AuthUser> {
    try {
      const userCredential = await confirmationResult.confirm(verificationCode);
      const user = userCredential.user as AuthUser;

      // Check if user exists in MongoDB
      const existingUser = await mongoService.getUserByAuthId(user.uid);

      if (!existingUser && profile) {
        // Create new user document
        await this.createMongoUser(user.uid, {
          ...profile,
          phoneNumber: user.phoneNumber || undefined,
        });
      } else if (existingUser) {
        // Update last login
        await mongoService.updateUserLastLogin(user.uid);
      }

      return user;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  // Alternative phone auth using credential (for React Native)
  async verifyPhoneWithCredential(
    verificationId: string,
    verificationCode: string,
    profile?: UserProfile,
  ): Promise<AuthUser> {
    try {
      const credential = PhoneAuthProvider.credential(verificationId, verificationCode);
      const userCredential = await signInWithCredential(auth, credential);
      const user = userCredential.user as AuthUser;

      // Check if user exists in MongoDB
      const existingUser = await mongoService.getUserByAuthId(user.uid);

      if (!existingUser && profile) {
        // Create new user document
        await this.createMongoUser(user.uid, {
          ...profile,
          phoneNumber: user.phoneNumber || undefined,
        });
      } else if (existingUser) {
        // Update last login
        await mongoService.updateUserLastLogin(user.uid);
      }

      return user;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  // User Management
  async signOut(): Promise<void> {
    try {
      await firebaseSignOut(auth);

      // Clear recaptcha verifier
      if (this.recaptchaVerifier) {
        this.recaptchaVerifier.clear();
        this.recaptchaVerifier = null;
      }
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async sendPasswordReset(email: string): Promise<void> {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async updateUserProfile(profile: Partial<UserProfile>): Promise<void> {
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('No authenticated user');
      }

      // Update Firebase profile if name changed
      if (profile.firstName || profile.lastName) {
        await updateProfile(user, {
          displayName: `${profile.firstName || ''} ${profile.lastName || ''}`.trim(),
        });
      }

      // Update MongoDB user document
      await mongoService.updateUser(user.uid, { profile });
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async deleteAccount(): Promise<void> {
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('No authenticated user');
      }

      // Delete from MongoDB first
      const mongoUser = await mongoService.getUserByAuthId(user.uid);
      if (mongoUser && mongoUser._id) {
        await mongoService.deleteUser(mongoUser._id);
      }

      // Delete Firebase user
      await deleteUser(user);
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  // MongoDB Integration
  async getMongoUser(authId: string): Promise<MongoUser | null> {
    try {
      const user = await mongoService.getUserByAuthId(authId);
      return user as MongoUser | null;
    } catch (error) {
      console.error('Failed to get MongoDB user:', error);
      return null;
    }
  }

  private async createMongoUser(authId: string, profile: UserProfile): Promise<UserDocument> {
    const userData: Omit<UserDocument, '_id' | 'createdAt' | 'updatedAt'> = {
      authId,
      profile: {
        ...profile,
        preferences: profile.preferences || {
          notifications: {
            email: true,
            push: true,
            sms: false,
          },
          privacy: {
            profileVisibility: 'public',
            showEmail: false,
            showPhone: false,
          },
          reading: {
            preferredTranslation: 'NIV',
            fontSize: 'medium',
            theme: 'auto',
          },
        },
      },
      locale: 'en',
      lastLogin: new Date(),
      circleIds: [],
      isActive: true,
      emailVerified: false,
      phoneVerified: false,
    };

    return await mongoService.createUser(userData);
  }

  // Google Sign-In
  async signInWithGoogle(): Promise<AuthUser> {
    try {
      if (Platform.OS === 'web') {
        return await this.signInWithGoogleWeb();
      } else {
        return await this.signInWithGoogleNative();
      }
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  private async signInWithGoogleWeb(): Promise<AuthUser> {
    const provider = new GoogleAuthProvider();
    provider.addScope('email');
    provider.addScope('profile');

    // For web, we need to use signInWithPopup or signInWithRedirect
    // This is a simplified implementation - in production, you'd use signInWithPopup
    throw new Error('Google Web authentication requires signInWithPopup implementation');
  }

  private async signInWithGoogleNative(): Promise<AuthUser> {
    const clientId = Platform.OS === 'ios'
      ? process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID_IOS
      : process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID_ANDROID;

    if (!clientId) {
      throw new Error('Google client ID not configured');
    }

    const request = new AuthSession.AuthRequest({
      clientId,
      scopes: ['openid', 'profile', 'email'],
      redirectUri: AuthSession.makeRedirectUri({ useProxy: true }),
      responseType: AuthSession.ResponseType.IdToken,
    });

    const result = await request.promptAsync({
      authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
    });

    if (result.type === 'success' && result.params.id_token) {
      const credential = GoogleAuthProvider.credential(result.params.id_token);
      const userCredential = await signInWithCredential(auth, credential);
      const user = userCredential.user as AuthUser;

      await this.handleSocialSignIn(user, 'google');
      return user;
    }

    throw new Error('Google sign-in was cancelled or failed');
  }

  // Apple Sign-In
  async signInWithApple(): Promise<AuthUser> {
    try {
      if (Platform.OS !== 'ios' || !AppleAuthentication) {
        throw new Error('Apple Sign-In is only available on iOS 13+');
      }

      const isAvailable = await AppleAuthentication.isAvailableAsync();
      if (!isAvailable) {
        throw new Error('Apple Sign-In is not available on this device');
      }

      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      if (credential.identityToken) {
        const provider = new OAuthProvider('apple.com');
        const authCredential = provider.credential({
          idToken: credential.identityToken,
          rawNonce: credential.realUserStatus?.toString(),
        });

        const userCredential = await signInWithCredential(auth, authCredential);
        const user = userCredential.user as AuthUser;

        await this.handleSocialSignIn(user, 'apple', {
          firstName: credential.fullName?.givenName || '',
          lastName: credential.fullName?.familyName || '',
        });

        return user;
      }

      throw new Error('Apple Sign-In failed to return identity token');
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  // Enhanced Phone Authentication with Anonymous Linking
  async signInWithPhone(phoneNumber: string): Promise<string> {
    try {
      // First, sign in anonymously if not already authenticated
      let currentUser = auth.currentUser;
      if (!currentUser) {
        const anonymousCredential = await signInAnonymously(auth);
        currentUser = anonymousCredential.user;
      }

      if (Platform.OS === 'web') {
        return await this.signInWithPhoneWeb(phoneNumber);
      } else {
        return await this.signInWithPhoneNative(phoneNumber);
      }
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  private async signInWithPhoneWeb(phoneNumber: string): Promise<string> {
    if (!this.recaptchaVerifier) {
      this.recaptchaVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
        size: 'invisible',
        callback: () => {
          console.log('reCAPTCHA solved');
        },
      });
    }

    const confirmationResult = await signInWithPhoneNumber(auth, phoneNumber, this.recaptchaVerifier);
    return confirmationResult.verificationId;
  }

  private async signInWithPhoneNative(phoneNumber: string): Promise<string> {
    // For React Native, we'll use Firebase's verifyPhoneNumber
    // This is a placeholder - actual implementation would use Firebase Admin SDK
    // or a cloud function to send OTP
    throw new Error('Phone authentication on native platforms requires backend implementation');
  }

  async verifyPhoneOTP(verificationId: string, code: string, profile?: UserProfile): Promise<AuthUser> {
    try {
      const credential = PhoneAuthProvider.credential(verificationId, code);
      const currentUser = auth.currentUser;

      let userCredential;
      if (currentUser && currentUser.isAnonymous) {
        // Link phone credential to anonymous user
        userCredential = await linkWithCredential(currentUser, credential);
      } else {
        // Sign in with phone credential
        userCredential = await signInWithCredential(auth, credential);
      }

      const user = userCredential.user as AuthUser;

      // Check if user exists in MongoDB
      const existingUser = await mongoService.getUserByAuthId(user.uid);

      if (!existingUser && profile) {
        // Create new user document
        await this.createMongoUser(user.uid, {
          ...profile,
          phoneNumber: user.phoneNumber || undefined,
        });
      } else if (existingUser) {
        // Update last login and phone verification status
        await mongoService.updateUser(user.uid, {
          lastLogin: new Date(),
          phoneVerified: true
        });
      }

      return user;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  // Biometric Authentication
  async enableBiometrics(): Promise<boolean> {
    try {
      const isAvailable = await LocalAuthentication.hasHardwareAsync();
      if (!isAvailable) {
        throw new Error('Biometric hardware not available');
      }

      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      if (!isEnrolled) {
        throw new Error('No biometric credentials enrolled');
      }

      const user = auth.currentUser;
      if (!user) {
        throw new Error('No authenticated user');
      }

      // Generate a custom token for the user
      const customToken = await user.getIdToken();

      // Store the token securely
      await SecureStore.setItemAsync('biometric_token', customToken);

      return true;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async signInWithBiometrics(): Promise<AuthUser> {
    try {
      const isAvailable = await LocalAuthentication.hasHardwareAsync();
      if (!isAvailable) {
        throw new Error('Biometric hardware not available');
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to sign in',
        fallbackLabel: 'Use passcode',
        cancelLabel: 'Cancel',
      });

      if (!result.success) {
        throw new Error('Biometric authentication failed');
      }

      // Retrieve stored token
      const storedToken = await SecureStore.getItemAsync('biometric_token');
      if (!storedToken) {
        throw new Error('No biometric credentials found. Please sign in normally first.');
      }

      // Sign in with custom token
      const userCredential = await signInWithCustomToken(auth, storedToken);
      const user = userCredential.user as AuthUser;

      // Update last login
      await mongoService.updateUserLastLogin(user.uid);

      return user;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  private async handleSocialSignIn(user: AuthUser, provider: string, additionalProfile?: Partial<UserProfile>): Promise<void> {
    // Check if user exists in MongoDB
    const existingUser = await mongoService.getUserByAuthId(user.uid);

    if (!existingUser) {
      // Create new user document
      const profile: UserProfile = {
        firstName: additionalProfile?.firstName || user.displayName?.split(' ')[0] || '',
        lastName: additionalProfile?.lastName || user.displayName?.split(' ').slice(1).join(' ') || '',
        email: user.email || '',
        phoneNumber: user.phoneNumber || undefined,
        dateOfBirth: undefined,
        gender: undefined,
        location: undefined,
        bio: undefined,
        preferences: {
          notifications: {
            email: true,
            push: true,
            sms: false,
          },
          privacy: {
            profileVisibility: 'public',
            showEmail: false,
            showPhone: false,
          },
          reading: {
            preferredTranslation: 'NIV',
            fontSize: 'medium',
            theme: 'auto',
          },
        },
      };

      await this.createMongoUser(user.uid, profile);
    } else {
      // Update last login
      await mongoService.updateUserLastLogin(user.uid);
    }
  }

  private handleAuthError(error: any): AuthError {
    const authError: AuthError = {
      code: error.code || 'auth/unknown-error',
      message: error.message || 'An unknown error occurred',
      details: error.details,
    };

    console.error('Auth Error:', authError);
    return authError;
  }

  // Utility methods
  getCurrentUser(): User | null {
    return auth.currentUser;
  }

  isAuthenticated(): boolean {
    return !!auth.currentUser;
  }

  async refreshToken(): Promise<string | null> {
    try {
      const user = auth.currentUser;
      if (user) {
        return await user.getIdToken(true);
      }
      return null;
    } catch (error) {
      console.error('Failed to refresh token:', error);
      return null;
    }
  }
}

// Singleton instance
export const authService = new AuthService();

// Export for testing
export { AuthService };
