import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useAuthState } from 'react-firebase-hooks/auth';
import { auth } from '../config/firebase';
import { authService } from '../services/auth';
import { AuthUser, MongoUser, AuthContextType, UserProfile } from '../types/auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [firebaseUser, firebaseLoading] = useAuthState(auth);
  const [mongoUser, setMongoUser] = useState<MongoUser | null>(null);
  const [loading, setLoading] = useState(true);

  // Sync MongoDB user when Firebase user changes
  useEffect(() => {
    const syncMongoUser = async () => {
      if (firebaseUser) {
        try {
          const user = await authService.getMongoUser(firebaseUser.uid);
          setMongoUser(user);
        } catch (error) {
          console.error('Failed to sync MongoDB user:', error);
          setMongoUser(null);
        }
      } else {
        setMongoUser(null);
      }
      setLoading(false);
    };

    if (!firebaseLoading) {
      syncMongoUser();
    }
  }, [firebaseUser, firebaseLoading]);

  // Handle loading state
  useEffect(() => {
    setLoading(firebaseLoading);
  }, [firebaseLoading]);

  const signInWithEmail = async (email: string, password: string): Promise<void> => {
    try {
      setLoading(true);
      await authService.signInWithEmail(email, password);
      // User state will be updated automatically via useAuthState
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  const signUpWithEmail = async (
    email: string,
    password: string,
    profile: Partial<UserProfile>,
  ): Promise<void> => {
    try {
      setLoading(true);
      await authService.signUpWithEmail(email, password, profile as UserProfile);
      // User state will be updated automatically via useAuthState
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  const signInWithPhone = async (phoneNumber: string): Promise<void> => {
    setLoading(true);
    // This will return a confirmation result for OTP verification
    // The actual implementation depends on your phone auth flow
    await authService.signInWithPhone(phoneNumber);
    setLoading(false);
  };

  const verifyPhoneOTP = async (verificationId: string, code: string): Promise<void> => {
    try {
      setLoading(true);
      await authService.verifyPhoneWithCredential(verificationId, code);
      // User state will be updated automatically via useAuthState
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  const signOut = async (): Promise<void> => {
    try {
      setLoading(true);
      await authService.signOut();
      setMongoUser(null);
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  const updateProfile = async (profile: Partial<UserProfile>): Promise<void> => {
    await authService.updateUserProfile(profile);

    // Refresh MongoDB user data
    if (firebaseUser) {
      const updatedUser = await authService.getMongoUser(firebaseUser.uid);
      setMongoUser(updatedUser);
    }
  };

  const deleteAccount = async (): Promise<void> => {
    try {
      setLoading(true);
      await authService.deleteAccount();
      setMongoUser(null);
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  const contextValue: AuthContextType = {
    user: firebaseUser as AuthUser | null,
    mongoUser,
    loading,
    signInWithEmail,
    signUpWithEmail,
    signInWithPhone,
    verifyPhoneOTP,
    signOut,
    updateProfile,
    deleteAccount,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Export context for testing
export { AuthContext };
